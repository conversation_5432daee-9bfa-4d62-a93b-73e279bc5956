name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  voice-ci:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        runtime: [python, node]
    steps:
      - uses: actions/checkout@v3

      - name: Set up Python
        if: matrix.runtime == 'python'
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Set up Node
        if: matrix.runtime == 'node'
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install Python deps
        if: matrix.runtime == 'python'
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install -r requirements-dev.txt

      - name: Run pytest
        if: matrix.runtime == 'python'
        run: |
          pytest -q --cov=. --cov-report=xml
          coverage report --fail-under=85

      - name: Upload coverage
        if: matrix.runtime == 'python'
        uses: codecov/codecov-action@v3
        with:
          files: coverage.xml
          fail_ci_if_error: true

      - name: Install Node deps
        if: matrix.runtime == 'node'
        run: npm install

      - name: Run Node tests
        if: matrix.runtime == 'node'
        run: npm test --workspaces calendar
