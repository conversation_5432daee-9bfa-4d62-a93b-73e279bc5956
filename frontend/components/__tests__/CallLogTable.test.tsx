import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CallLogTable, { CallLog } from '../CallLogTable';

const mockLogs: CallLog[] = [
  { id: '1', date: '2024-01-01', caller: '<PERSON>', duration: 120, transcriptUrl: '/t1', costSaved: 5 },
  { id: '2', date: '2024-01-02', caller: '<PERSON>', duration: 60, transcriptUrl: '/t2', costSaved: 3 }
];

global.fetch = jest.fn(() => Promise.resolve({
  ok: true,
  json: () => Promise.resolve(mockLogs)
})) as jest.Mock;

describe('CallLogTable', () => {
  it('renders rows from API', async () => {
    render(<CallLogTable />);
    expect(fetch).toHaveBeenCalledWith('/api/call-log');

    await waitFor(() => expect(screen.getAllByTestId('call-row').length).toBe(2));
    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
  });

  it('exports CSV when button clicked', async () => {
    render(<CallLogTable />);
    await waitFor(() => screen.getByTestId('export-csv'));
    const createObjectURLSpy = jest.spyOn(URL, 'createObjectURL');
    screen.getByTestId('export-csv').click();
    expect(createObjectURLSpy).toHaveBeenCalled();
  });
});
