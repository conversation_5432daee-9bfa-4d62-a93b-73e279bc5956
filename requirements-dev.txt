# Core testing and migration dependencies
pytest>=8.3
pytest-asyncio>=0.25
pytest-cov>=4.0.0
coverage>=7.0.0
python-dotenv>=1.0
alembic>=1.13

# Linting and type checking
ruff>=0.4
mypy>=1.10

# Database and ORM
SQLAlchemy<2.0.0,>=1.4.0
sqlite-utils>=3.35.0
websockets>=12.0
aiohttp>=3.9

# FastAPI and web dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
httpx>=0.25.0

# Development utilities
black>=24.4.0
isort>=5.13.2
pydantic<2,>=1.10.15
