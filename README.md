# AI Lex Receptionist

A 24/7 AI voice receptionist for law firms that never lets a call go to voicemail.

## ✨ Features

- **24/7 Call Handling**: Never miss a client call, even after hours
- **Natural Conversations**: AI-powered voice interactions that feel human-like
- **Smart Scheduling**: Seamless calendar integration for booking appointments
- **Multi-Platform**: Web dashboard and voice interface for complete flexibility
- **Secure & Compliant**: Built with legal industry security standards in mind

## 🏗️ Project Structure

```
.
├── apps/
│   ├── voice-agent/      # Pipecat voice agent (Python)
│   ├── calendar-svc/     # Calendar service (FastAPI)
│   └── dashboard/        # Admin dashboard (Next.js + shadcn/ui)
├── packages/
│   └── shared/          # Shared types and utilities
└── docs/                 # Project documentation
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ and pnpm
- Python 3.10+
- PostgreSQL 14+
- Redis 6+

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/ailex-receptionist.git
   cd ailex-receptionist
   ```

2. Install dependencies:
   ```bash
   pnpm install
   cd apps/calendar-svc
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your credentials
   ```

4. Start development servers:
   ```bash
   # In the project root
   pnpm dev
   ```

## 🛠️ Development

### Available Commands

- `pnpm dev` - Start all services in development mode
- `pnpm build` - Build all services for production
- `pnpm format` - Format code with Prettier
- `pnpm lint` - Lint code with ESLint
- `pnpm test` - Run tests across all services

### Utility Modules

The project includes several reusable utility modules:

- **HTTP Client**: Robust async HTTP client with retries and error handling
- **Caching**: Flexible caching system with Redis and in-memory backends
- **Error Handling**: Comprehensive exception hierarchy for consistent error responses
- **Date/Time**: Timezone-aware datetime utilities
- **File Operations**: Safe file handling with support for various formats
- **Validation**: Input validation and sanitization
- **Logging**: Structured logging with request ID tracking

## 🚦 Roadmap

### ✅ Completed
- [x] Project setup and architecture
- [x] Core utility modules
  - [x] HTTP client with retries
  - [x] Caching system
  - [x] Error handling
  - [x] Logging infrastructure
  - [x] Validation utilities
  - [x] File operations
  - [x] Date/time handling
  - [x] Background tasks

### 🚧 In Progress
- [ ] Calendar service
  - [ ] Google Calendar integration
  - [ ] Appointment scheduling
  - [ ] Availability management

### 📅 Planned
- [ ] Voice agent
  - [ ] Call handling
  - [ ] Speech-to-text
  - [ ] Natural language processing
- [ ] Dashboard
  - [ ] User authentication
  - [ ] Call analytics
  - [ ] Appointment management
- [ ] Integrations
  - [ ] CRM systems
  - [ ] Practice management software
  - [ ] Payment processors

## 📚 Documentation

- [Calendar Service](./apps/calendar-svc/README.md)
- [Voice Agent](./apps/voice-agent/README.md)
- [Dashboard](./apps/dashboard/README.md)
- [API Reference](./docs/API.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary and confidential. All rights reserved.

---

Built with ❤️ by AI Lex Team
