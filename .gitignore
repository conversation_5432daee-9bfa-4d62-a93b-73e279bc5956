# Dependencies
node_modules
.next
dist
build
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Local env files
.env*.local

# IDE
.idea
.vscode
*.swp
*.swo

# Python
__pycache__
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
.venv/
env.bak/
venv.bak/

# Docker
.docker
docker-compose.override.yml

# Local Netlify folder
.netlify

# Supabase
supabase/migrations
supabase/.temp
supabase/.branches
supabase/.git-lfs
