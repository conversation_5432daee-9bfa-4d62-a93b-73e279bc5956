import importlib
import sys
import types

import pytest


@pytest.fixture(autouse=True)
def auth_stub(monkeypatch):
    calls = []

    def fake_get_access_token(firm_id: str, provider: str) -> str:
        calls.append((firm_id, provider))
        return f"token-{provider}"

    module = types.SimpleNamespace(get_access_token=fake_get_access_token)
    sys.modules['ailex_auth'] = module
    yield calls
    sys.modules.pop('ailex_auth')


def test_get_provider_google(auth_stub):
    from packages.calendar_core import get_provider
    from packages.calendar_core.adapters.google import GoogleCalendarProvider

    provider = get_provider("firm1", "google")
    assert isinstance(provider, GoogleCalendarProvider)
    assert provider.token == "token-google"
    assert auth_stub == [("firm1", "google")]


@pytest.mark.asyncio
async def test_provider_methods(auth_stub):
    from packages.calendar_core import get_provider

    provider = get_provider("firm1", "calendly")
    calendars = await provider.list_calendars()
    assert calendars
    event = await provider.create_event("primary", {"summary": "Test"})
    assert event["id"]
