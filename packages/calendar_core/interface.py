from __future__ import annotations

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime

from ailex_auth import get_access_token


class CalendarProvider(ABC):
    """Abstract calendar provider."""

    def __init__(self, firm_id: str, provider_name: str, capability: Optional[Dict[str, Any]] = None) -> None:
        self.firm_id = firm_id
        self.provider_name = provider_name
        self.capability = capability or {}
        # Retrieve token via shared auth client
        self.token = get_access_token(firm_id, provider_name)

    @abstractmethod
    async def list_calendars(self) -> List[Dict[str, Any]]:
        """Return calendars accessible by the firm."""

    @abstractmethod
    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]:
        """Create a calendar event."""

    async def get_availability(
        self, calendar_ids: List[str], time_min: datetime, time_max: datetime
    ) -> Any:
        """Optional availability retrieval."""
        raise NotImplementedError
