from __future__ import annotations

from typing import Any, Dict, List

from ..interface import CalendarProvider


class CalendlyProvider(CalendarProvider):
    """Minimal Calendly adapter."""

    async def list_calendars(self) -> List[Dict[str, Any]]:
        # Calendly exposes event types rather than calendars; this is a stub
        return [{"id": "primary", "name": "Calendly"}]

    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]:
        return {"id": "c_evt_1", **event}
