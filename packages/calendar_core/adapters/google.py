from __future__ import annotations

from typing import Any, Dict, List

from ..interface import CalendarProvider


class GoogleCalendarProvider(CalendarProvider):
    """Minimal Google Calendar adapter."""

    async def list_calendars(self) -> List[Dict[str, Any]]:
        # Mocked response
        return [{"id": "primary", "summary": "Primary"}]

    async def create_event(self, calendar_id: str, event: Dict[str, Any]) -> Dict[str, Any]:
        # Mocked creation result
        return {"id": "g_evt_1", **event}
